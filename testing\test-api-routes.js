const axios = require('axios');

// Test all API routes to make sure they're working
async function testApiRoutes() {
  const API_GATEWAY = 'http://localhost:3000';
  
  console.log('🧪 Testing API Routes...\n');

  // Test 1: Gateway Health Check
  console.log('1️⃣ Testing Gateway Health...');
  try {
    const response = await axios.get(`${API_GATEWAY}/health`);
    console.log('✅ Gateway Health:', response.data.status);
  } catch (error) {
    console.log('❌ Gateway Health failed:', error.message);
    return;
  }

  // Test 2: Detailed Health Check
  console.log('\n2️⃣ Testing Detailed Health...');
  try {
    const response = await axios.get(`${API_GATEWAY}/health/detailed`);
    console.log('✅ Services Status:');
    Object.entries(response.data.services).forEach(([service, status]) => {
      console.log(`   ${service}: ${status.status} (${status.responseTime}ms)`);
    });
  } catch (error) {
    console.log('❌ Detailed Health failed:', error.message);
  }

  // Test 3: Auth Service Routes
  console.log('\n3️⃣ Testing Auth Service Routes...');
  
  // Test Register
  const testEmail = '<EMAIL>';
  const testPassword = 'password123';
  let token = null;

  try {
    console.log('   📝 Testing Register...');
    const registerResponse = await axios.post(`${API_GATEWAY}/api/auth/register`, {
      email: testEmail,
      password: testPassword
    });
    console.log('   ✅ Register:', registerResponse.data.success ? 'Success' : 'Failed');
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('   ⚠️ Register: User already exists');
    } else {
      console.log('   ❌ Register failed:', error.response?.data?.error?.message || error.message);
    }
  }

  // Test Login
  try {
    console.log('   🔑 Testing Login...');
    const loginResponse = await axios.post(`${API_GATEWAY}/api/auth/login`, {
      email: testEmail,
      password: testPassword
    });
    
    if (loginResponse.data.success && loginResponse.data.data.token) {
      token = loginResponse.data.data.token;
      console.log('   ✅ Login: Success');
    } else {
      console.log('   ❌ Login: No token received');
    }
  } catch (error) {
    console.log('   ❌ Login failed:', error.response?.data?.error?.message || error.message);
    return;
  }

  // Test Profile
  if (token) {
    try {
      console.log('   👤 Testing Profile...');
      const profileResponse = await axios.get(`${API_GATEWAY}/api/auth/profile`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('   ✅ Profile: Success');
    } catch (error) {
      console.log('   ❌ Profile failed:', error.response?.data?.error?.message || error.message);
    }
  }

  // Test 4: Assessment Service Routes
  console.log('\n4️⃣ Testing Assessment Service Routes...');
  
  if (token) {
    // Test Assessment Submit
    try {
      console.log('   📊 Testing Assessment Submit...');
      const assessmentData = {
        riasec: {
          realistic: 75, investigative: 85, artistic: 60,
          social: 50, enterprising: 70, conventional: 55
        },
        ocean: {
          conscientiousness: 65, extraversion: 55, agreeableness: 45,
          neuroticism: 30, openness: 80
        },
        viaIs: {
          creativity: 85, curiosity: 78, judgment: 70, loveOfLearning: 82, perspective: 60,
          bravery: 75, perseverance: 80, honesty: 90, zest: 70, love: 85,
          kindness: 88, socialIntelligence: 75, teamwork: 82, fairness: 85, leadership: 70,
          forgiveness: 75, humility: 65, prudence: 70, selfRegulation: 75, appreciationOfBeauty: 80,
          gratitude: 85, hope: 80, humor: 75, spirituality: 60
        }
      };

      const assessmentResponse = await axios.post(
        `${API_GATEWAY}/api/assessment/submit`,
        assessmentData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (assessmentResponse.data.success) {
        const jobId = assessmentResponse.data.data.jobId;
        console.log('   ✅ Assessment Submit: Success, jobId:', jobId);
        
        // Test job status check
        setTimeout(async () => {
          try {
            console.log('   📋 Testing Job Status...');
            const jobResponse = await axios.get(
              `${API_GATEWAY}/api/archive/jobs/${jobId}`,
              {
                headers: { 'Authorization': `Bearer ${token}` }
              }
            );
            console.log('   ✅ Job Status:', jobResponse.data.data.status);
          } catch (error) {
            console.log('   ❌ Job Status failed:', error.response?.data?.error?.message || error.message);
          }
        }, 2000);
        
      } else {
        console.log('   ❌ Assessment Submit: Failed');
      }
    } catch (error) {
      console.log('   ❌ Assessment Submit failed:', error.response?.data?.error?.message || error.message);
      if (error.response?.data?.error?.details) {
        console.log('   📋 Validation errors:', error.response.data.error.details);
      }
    }
  }

  // Test 5: Archive Service Routes
  console.log('\n5️⃣ Testing Archive Service Routes...');
  
  if (token) {
    try {
      console.log('   📊 Testing Archive Results...');
      const resultsResponse = await axios.get(`${API_GATEWAY}/api/archive/results`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('   ✅ Archive Results: Success, found', resultsResponse.data.data.length, 'results');
    } catch (error) {
      console.log('   ❌ Archive Results failed:', error.response?.data?.error?.message || error.message);
    }

    try {
      console.log('   📋 Testing Archive Jobs...');
      const jobsResponse = await axios.get(`${API_GATEWAY}/api/archive/jobs`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('   ✅ Archive Jobs: Success, found', jobsResponse.data.data.length, 'jobs');
    } catch (error) {
      console.log('   ❌ Archive Jobs failed:', error.response?.data?.error?.message || error.message);
    }
  }

  // Test 6: Development Endpoints (if available)
  console.log('\n6️⃣ Testing Development Endpoints...');
  
  try {
    console.log('   🧪 Testing Assessment Test Submit...');
    const testData = {
      riasec: { realistic: 50, investigative: 60, artistic: 70, social: 80, enterprising: 90, conventional: 40 },
      ocean: { conscientiousness: 50, extraversion: 60, agreeableness: 70, neuroticism: 30, openness: 80 },
      viaIs: {
        creativity: 85, curiosity: 78, judgment: 70, loveOfLearning: 82, perspective: 60,
        bravery: 75, perseverance: 80, honesty: 90, zest: 70, love: 85,
        kindness: 88, socialIntelligence: 75, teamwork: 82, fairness: 85, leadership: 70,
        forgiveness: 75, humility: 65, prudence: 70, selfRegulation: 75, appreciationOfBeauty: 80,
        gratitude: 85, hope: 80, humor: 75, spirituality: 60
      }
    };

    const testResponse = await axios.post(`${API_GATEWAY}/api/assessment/test/submit`, testData);
    console.log('   ✅ Test Submit: Success, jobId:', testResponse.data.data.jobId);
  } catch (error) {
    console.log('   ❌ Test Submit failed:', error.response?.data?.error?.message || error.message);
  }

  console.log('\n🎉 API Route testing completed!');
}

// Run the test
testApiRoutes().catch(console.error);
