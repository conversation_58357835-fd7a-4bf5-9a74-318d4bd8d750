const axios = require('axios');

// Super simple test untuk 1 user saja
async function simpleTest() {
  const API_GATEWAY = 'http://localhost:3000';
  const userEmail = '<EMAIL>';
  const userPassword = 'password123';

  console.log('🧪 Running simple single user test...\n');

  try {
    // 1. Register
    console.log('1️⃣ Registering user...');
    try {
      await axios.post(`${API_GATEWAY}/api/auth/register`, {
        email: userEmail,
        password: userPassword
      });
      console.log('✅ Registration successful');
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('⚠️ User already exists, continuing...');
      } else {
        throw error;
      }
    }

    // 2. Login
    console.log('\n2️⃣ Logging in...');
    const loginResponse = await axios.post(`${API_GATEWAY}/api/auth/login`, {
      email: userEmail,
      password: userPassword
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');

    // 3. Submit Assessment
    console.log('\n3️⃣ Submitting assessment...');
    const assessmentData = {
      riasec: {
        realistic: 75,
        investigative: 85,
        artistic: 60,
        social: 50,
        enterprising: 70,
        conventional: 55
      },
      ocean: {
        conscientiousness: 65,
        extraversion: 55,
        agreeableness: 45,
        neuroticism: 30,
        openness: 80
      },
      viaIs: {
        creativity: 85,
        curiosity: 78,
        judgment: 70,
        loveOfLearning: 82,
        perspective: 60,
        bravery: 75,
        perseverance: 80,
        honesty: 90,
        zest: 70,
        love: 85,
        kindness: 88,
        socialIntelligence: 75,
        teamwork: 82,
        fairness: 85,
        leadership: 70,
        forgiveness: 75,
        humility: 65,
        prudence: 70,
        selfRegulation: 75,
        appreciationOfBeauty: 80,
        gratitude: 85,
        hope: 80,
        humor: 75,
        spirituality: 60
      }
    };

    const assessmentResponse = await axios.post(
      `${API_GATEWAY}/api/assessment/submit`,
      assessmentData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const jobId = assessmentResponse.data.data.jobId;
    console.log(`✅ Assessment submitted, jobId: ${jobId}`);

    // 4. Check job status (polling)
    console.log('\n4️⃣ Checking job status...');
    let resultId = null;
    
    for (let i = 0; i < 30; i++) { // Max 30 attempts (60 seconds)
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      
      try {
        const jobResponse = await axios.get(
          `${API_GATEWAY}/api/archive/jobs/${jobId}`,
          {
            headers: { 'Authorization': `Bearer ${token}` }
          }
        );
        
        const status = jobResponse.data.data.status;
        console.log(`📊 Job status: ${status}`);
        
        if (status === 'completed') {
          resultId = jobResponse.data.data.resultId;
          console.log(`✅ Job completed, resultId: ${resultId}`);
          break;
        } else if (status === 'failed') {
          throw new Error('Job failed');
        }
      } catch (error) {
        console.log(`⚠️ Error checking job status: ${error.message}`);
      }
    }

    // 5. Retrieve result data
    if (resultId) {
      console.log('\n5️⃣ Retrieving analysis result...');
      const resultResponse = await axios.get(
        `${API_GATEWAY}/api/archive/results/${resultId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      console.log('✅ Data retrieved successfully');
      console.log('📊 Result summary:', {
        resultId: resultResponse.data.data.id,
        userId: resultResponse.data.data.userId,
        status: resultResponse.data.data.status,
        createdAt: resultResponse.data.data.createdAt
      });
    } else {
      console.log('❌ Job did not complete within timeout');
    }

    console.log('\n🎉 Simple test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
simpleTest();
