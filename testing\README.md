# ATMA Load Testing

Program sederhana untuk menguji user flow integrasi ekosistem ATMA dengan multiple concurrent users.

## User Flow yang Ditest

1. **Register** - Registrasi user baru di auth-service
2. **Login** - Login dan mendapat JWT token
3. **Submit Assessment** - Submit data assessment ke assessment-service
4. **Receive Notification** - Terima notifikasi via WebSocket dari notification-service
5. **Retrieve Data** - Ambil hasil analisis dari archive-service

## Prerequisites

Pastikan semua service ATMA sudah berjalan:

```bash
# Terminal 1 - Auth Service
cd auth-service && npm start

# Terminal 2 - Archive Service  
cd archive-service && npm start

# Terminal 3 - Assessment Service
cd assessment-service && npm start

# Terminal 4 - API Gateway
cd api-gateway && npm start

# Terminal 5 - Notification Service
cd notification-service && npm start

# Terminal 6 - Analysis Worker (optional, untuk processing assessment)
cd analysis-worker && npm start
```

## Installation

```bash
cd testing
npm install
```

## Usage

### Test dengan 250 users (default)
```bash
npm test
# atau
npm run test:250
```

### Test dengan jumlah user berbeda
```bash
npm run test:50   # 50 users
npm run test:10   # 10 users

# atau custom
node test-user-flow.js 100  # 100 users
```

### Test sederhana dan debugging
```bash
npm run test:simple   # Test 1 user saja
npm run test:routes   # Test semua API routes
```

## Configuration

Edit `test-user-flow.js` untuk mengubah konfigurasi:

```javascript
const CONFIG = {
  API_GATEWAY: 'http://localhost:3000',
  NOTIFICATION_WS: 'ws://localhost:3005', 
  CONCURRENT_USERS: 250,
  DELAY_BETWEEN_BATCHES: 100, // ms
  BATCH_SIZE: 10
};
```

## Output

Program akan menampilkan:
- Progress real-time untuk setiap user
- Statistik akhir dengan persentase keberhasilan
- Throughput (users/second)
- Total durasi test

## Sample Output

```
🚀 Starting load test with 250 concurrent users
📊 Batch size: 10, Delay: 100ms
============================================================
[User 1] Starting user flow...
[User 1] ✅ Registered successfully
[User 1] ✅ Logged in successfully
[User 1] ✅ Assessment submitted, jobId: job_123
[User 1] 🔌 Connected to notification service
[User 1] 🔔 Notification received, resultId: result_456
[User 1] ✅ Data retrieved successfully
[User 1] 🎉 User flow completed successfully!

============================================================
📈 LOAD TEST RESULTS
============================================================
Total Users: 250
✅ Registered: 250 (100.0%)
🔑 Logged In: 248 (99.2%)
📝 Assessment Submitted: 245 (98.0%)
🔔 Notification Received: 240 (96.0%)
📊 Data Retrieved: 238 (95.2%)
❌ Errors: 12 (4.8%)
⏱️ Duration: 45.67 seconds
🚀 Throughput: 5.47 users/second
============================================================
```

## Files

- **`test-user-flow.js`** - Load testing utama dengan multiple concurrent users
- **`simple-test.js`** - Test sederhana untuk 1 user (debugging)
- **`test-api-routes.js`** - Test semua API routes dan endpoint
- **`install-and-run.bat`** - Quick start script untuk Windows

## Notes

- Program menggunakan batch processing untuk menghindari overwhelming system
- Setiap user menggunakan email unik: `testuser{id}@example.com`
- Password default: `password123`
- Timeout untuk notifikasi: 30 detik
- Jika notifikasi timeout, program akan fallback ke manual job status checking
- Data assessment menggunakan nilai random untuk testing
- **VIA-IS sekarang lengkap dengan 24 character strengths** (sesuai schema validation)
- **API routing sudah diperbaiki** menggunakan endpoint yang benar

## Troubleshooting

1. **Connection refused**: Pastikan semua service sudah running
2. **High error rate**: Kurangi `CONCURRENT_USERS` atau perbesar `DELAY_BETWEEN_BATCHES`
3. **Notification timeout**: Pastikan analysis-worker running untuk memproses assessment
4. **Memory issues**: Kurangi `BATCH_SIZE` atau `CONCURRENT_USERS`
