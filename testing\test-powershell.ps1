# PowerShell script untuk test API
Write-Host "Testing ATMA API with PowerShell..." -ForegroundColor Cyan

$API_GATEWAY = "http://localhost:3000"

# Test 1: Health Check
Write-Host "`n1. Testing Health Check..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$API_GATEWAY/health" -Method GET
    Write-Host "SUCCESS Health Check: $($healthResponse.status)" -ForegroundColor Green
} catch {
    Write-Host "FAILED Health Check: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Register
Write-Host "`n2. Testing Register..." -ForegroundColor Yellow
$registerData = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $registerResponse = Invoke-RestMethod -Uri "$API_GATEWAY/api/auth/register" -Method POST -Body $registerData -ContentType "application/json"
    Write-Host "SUCCESS Register: Success" -ForegroundColor Green
} catch {
    if ($_.Exception.Response.StatusCode -eq 409) {
        Write-Host "WARNING Register: User already exists" -ForegroundColor Yellow
    } else {
        Write-Host "FAILED Register: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Test 3: Login
Write-Host "`n3. Testing Login..." -ForegroundColor Yellow
$loginData = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$API_GATEWAY/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    $token = $loginResponse.data.token
    Write-Host "SUCCESS Login: Success, token received" -ForegroundColor Green
} catch {
    Write-Host "FAILED Login: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 4: Assessment Submit
Write-Host "`n4. Testing Assessment Submit..." -ForegroundColor Yellow
$assessmentData = @{
    riasec = @{
        realistic = 75
        investigative = 85
        artistic = 60
        social = 50
        enterprising = 70
        conventional = 55
    }
    ocean = @{
        conscientiousness = 65
        extraversion = 55
        agreeableness = 45
        neuroticism = 30
        openness = 80
    }
    viaIs = @{
        creativity = 85
        curiosity = 78
        judgment = 70
        loveOfLearning = 82
        perspective = 60
        bravery = 75
        perseverance = 80
        honesty = 90
        zest = 70
        love = 85
        kindness = 88
        socialIntelligence = 75
        teamwork = 82
        fairness = 85
        leadership = 70
        forgiveness = 75
        humility = 65
        prudence = 70
        selfRegulation = 75
        appreciationOfBeauty = 80
        gratitude = 85
        hope = 80
        humor = 75
        spirituality = 60
    }
} | ConvertTo-Json -Depth 3

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $assessmentResponse = Invoke-RestMethod -Uri "$API_GATEWAY/api/assessment/submit" -Method POST -Body $assessmentData -Headers $headers
    $jobId = $assessmentResponse.data.jobId
    Write-Host "✅ Assessment Submit: Success, jobId: $jobId" -ForegroundColor Green
    
    # Test 5: Job Status
    Write-Host "`n5️⃣ Testing Job Status..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    
    try {
        $jobResponse = Invoke-RestMethod -Uri "$API_GATEWAY/api/archive/jobs/$jobId" -Method GET -Headers @{"Authorization" = "Bearer $token"}
        Write-Host "✅ Job Status: $($jobResponse.data.status)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Job Status failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Assessment Submit failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.ErrorDetails.Message) {
        $errorDetails = $_.ErrorDetails.Message | ConvertFrom-Json
        Write-Host "📋 Error details: $($errorDetails.error.message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 PowerShell API test completed!" -ForegroundColor Cyan
